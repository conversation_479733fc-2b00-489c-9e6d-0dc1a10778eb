{{ 'quiz-result.css' | asset_url | stylesheet_tag: media: 'screen' }}

<div class="quiz-result-banner {{ section.settings.custom_class }}">
  <div class="container">
    <div class="quiz-result-inner">
      <div class="quiz-result-heading">
        {% if section.settings.title != blank %}
          <div class="quiz-result-title">
            <h2>{{ section.settings.title }}</h2>
          </div>
        {% endif %}
        <div class="quiz-result-subtitle">
          <span class="subtitle-quantity">1 items</span>
          <span class="subtitle-subtotal">$458</span>
          <span class="subtitle-total">$249</span>
        </div>
      </div>
      <div class="quiz-result-wrapper">
        <div class="quiz-result-product">
          <div class="quiz-result-product-list">
            <div class="result-product">
              <div class="result-product-wrap">
                <div class="result-product-image">
                  <img src="https://upstep-custom-orthotics.myshopify.com/cdn/shop/files/d774ad378abc8c46687b93c6.webp">
                </div>
                <div class="result-product-info">
                  <div class="result-product-name">
                    <h3>
                      <a href="https://upstep-custom-orthotics.myshopify.com/products/on-my-feet-all-day-custom-orthotics">
                        On My Feet All Day – Custom Orthotics
                      </a>
                    </h3>
                  </div>
                  <div class="result-product-price">
                    <span class="product-old-price">$458</span>
                    <span class="product-new-price">SALE $249</span>
                  </div>
                  <div class="result-product-quantity">
                    <div class="result-product-remove">Remove</div>
                    <div class="product-quantity-count">
                      <button type="button" class="quantity-btn_plus">
                        <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" fill="none">
                          <circle cx="10.5" cy="10.5" r="10.5" fill="#F9F9F9"/><path fill="#A0A0A0" d="M5.709 9.2a.75.75 0 1 0 0 1.5zm4.948 1.5a.75.75 0 0 0 0-1.5zm0-1.5a.75.75 0 0 0 0 1.5zm4.949 1.5a.75.75 0 0 0 0-1.5zm-4.199-.75a.75.75 0 0 0-1.5 0zm-1.5 4.947a.75.75 0 1 0 1.5 0zm0-4.949a.75.75 0 1 0 1.5 0zM11.407 5a.75.75 0 0 0-1.5 0zM5.71 10.7h4.948V9.2H5.709zm4.949 0h4.948V9.2h-4.948zm-.75-.75v4.947h1.5V9.95zm1.5-.002V5h-1.5v4.948z"/>
                        </svg>
                      </button>
                      <div class="quantity-value_text"><span class="product-quantity">1</span> <span>Pair</span></div>
                      <button type="button" class="quantity-btn_minus">
                        <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" fill="none">
                          <circle cx="10.5" cy="10.5" r="10.5" fill="#F9F9F9"/><path fill="#A0A0A0" d="M6 10.2a.75.75 0 0 0 0 1.5zm4.95 1.5a.75.75 0 0 0 0-1.5zm0-1.5a.75.75 0 0 0 0 1.5zm4.95 1.5a.75.75 0 0 0 0-1.5zm-9.9 0h4.95v-1.5H6zm4.95 0h4.95v-1.5h-4.95z"/>
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
                <div class="result-product-content">
                  <h4 class="product-content-title accordion-toggle">Your Upstep Info</h4>
                  <div class="product-content-list accordion-content">
                    <p><strong>What shoe size do you wear?:</strong> 7</p>
                    <p><strong>Pains:</strong> Right and Left The arch</p>
                    <p><strong>Diagnosis:</strong> Right and Left Plantar Fasciitis</p>
                    <p><strong>Sport:</strong> Basketball</p>
                    <p><strong>Basketball Shoes:</strong> Low/Mid Top</p>
                    <p><strong>Gender:</strong> Man</p>
                  </div>
                </div>
              </div>
              <div class="result-product-removing_message">
                <div class="result-product-message_wrapper">
                  <div class="result-product-form_block">
                    <div class="result-product-message">Remove from cart?</div>
                    <div class="result-product-buttons_wrapper">
                      <button type="button" class="btn btn_light_blue yes-remove-product">Yes</button>
                      <button type="button" class="btn btn_gray no-remove-product">No</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="result-suggest-product">
            {% if section.settings.suggest_title != blank %}
              <div class="result-suggest-title">
                <h3>{{ section.settings.suggest_title }}</h3>
              </div>
            {% endif %}
            <div class="result-suggest-list">
              {% for product in section.settings.product_list %}
                <div class="suggest-product-block">
                  <div class="suggest-product-image">
                    <img src="{{ product.featured_image | img_url: '300x' }}" alt="{{ product.title }}">
                  </div>
                  <div class="suggest-product-info">
                    <div class="suggest-product-content">
                      <div class="suggest-product-title">
                        <h5>{{ product.title }}</h5>
                      </div>
                      <div class="suggest-product-price">
                        <span class="suggest-product-price-new">
                          {{ product.price | money }}
                        </span>
                        {% if product.compare_at_price > product.price %}
                          <span class="suggest-product-price-old">
                            {{ product.compare_at_price | money }}
                          </span>
                        {% endif %}
                      </div>
                    </div>
                    <div class="suggest-product-add">
                      <button class="suggest-product-add-btn" type="button">Add</button>
                    </div>
                  </div>
                </div>
              {% endfor %}
            </div>
          </div>
        </div>
        <div class="quiz-result-cart">
          <div class="quiz-result-cart-title">Summary</div>
          <div class="quiz-result-cart-info">
            <div class="result-shipping">
              <div class="result-shipping_img">
                <img
                  src="https://dcpsvjmu5hpqr.cloudfront.net/images/2021-11/638755d51c7bb99069d28f86.svg"
                  alt="Free &amp; fast shipping.svg"
                >
              </div>
              <div class="result-shipping_title">Free Shipping</div>
            </div>
            <div class="result-cart-top">
              <div class="result-cart-subtotal result-cart-text">
                <div class="cart-subtotal-label">Subtotal</div>
                <div class="cart-subtotal-price">$458</div>
              </div>
              <div class="result-cart-shipping result-cart-text">
                <div class="cart-shipping-label">Shipping</div>
                <div class="cart-shipping-price">FREE</div>
              </div>
              <div class="result-cart-discounts result-cart-text">
                <div class="cart-discounts-label">Discounts</div>
                <div class="cart-discounts-price">- $209</div>
              </div>
            </div>
            <div class="result-cart-bottom">
              <div class="result-cart-total">
                <div class="cart-discounts-label">Total:</div>
                <div class="cart-discounts-price">$249</div>
              </div>
              <div class="result-cart-submit">
                <button class="overlay-buy_button" type="submit">Secure Checkout</button>
              </div>
              <ul class="result-cart-payment">
                {%- for payment_method in shop.enabled_payment_types -%}
                  <li>{{ payment_method | payment_type_svg_tag }}</li>
                {%- endfor -%}
              </ul>
              <div class="result-cart-free-shipping">
                <svg xmlns="http://www.w3.org/2000/svg" width="11" height="12" fill="none">
                  <path stroke="#53BA65" stroke-linecap="round" stroke-width="2" d="m10 2-5.143 8M1 5.2 4.857 10"/>
                </svg>
                <p>Your order is eligible for free express shipping</p>
              </div>
            </div>
          </div>
          <ul class="result-cart-payment-mobile">
            {%- for payment_method in shop.enabled_payment_types -%}
              <li>{{ payment_method | payment_type_svg_tag }}</li>
            {%- endfor -%}
          </ul>
        </div>
      </div>
    </div>
  </div>
  <div class="quiz-result-fixed_button">
    <div class="quiz-result-button_wrapper">
      <div class="quiz-result-total_row">
        <div class="result-fixed-total-label">Total</div>
        <div class="result-fixed-total_price">
          <div class="result-fixed-price_old">$458</div>
          <div class="result-fixed-price_new">$249</div>
        </div>
      </div>
      <button class="result-fixed-btn" type="button">Secure Checkout</button>
      <div class="result-fixed-bottom-line">180-day money-back guarantee</div>
    </div>
  </div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script>
  $(document).ready(function () {
    function initAccordion() {
      if ($(window).width() <= 767) {
        $('.accordion-toggle')
          .off('click')
          .on('click', function () {
            $(this).toggleClass('active');
            $(this).next('.accordion-content').slideToggle();
          });
      } else {
        $('.accordion-content').show();
        $('.accordion-toggle').removeClass('active');
      }
    }

    initAccordion();
    $(window).resize(function () {
      initAccordion();
    });


    $(".quantity-btn_plus").click(function(){
      let $qty = $(this).siblings(".quantity-value_text").find(".product-quantity");
      let currentValue = parseInt($qty.text());
      $qty.text(currentValue + 1);
    });

    $(".quantity-btn_minus").click(function(){
      let $qty = $(this).siblings(".quantity-value_text").find(".product-quantity");
      let currentValue = parseInt($qty.text());

      if(currentValue > 1){
        $qty.text(currentValue - 1);
      } else {
        if($(window).width() <= 767){
          $(".result-product-removing_message").addClass("show-remove-message");
        }
      }
    });

    $(".result-product-remove").click(function(){
      $(".result-product-removing_message").addClass("show-remove-message");
    });

    $(".result-product-removing_message .no-remove-product").click(function(){
      $(".result-product-removing_message").removeClass("show-remove-message");
    });
  });
</script>

{% schema %}
{
  "name": "Quiz result banner",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "text",
      "id": "custom_class",
      "label": "Custom class"
    },
    {
      "type": "inline_richtext",
      "id": "title",
      "label": "Title"
    },
    {
      "type": "inline_richtext",
      "id": "suggest_title",
      "label": "Suggest title"
    },
    {
      "type": "product_list",
      "id": "product_list",
      "label": "Select product"
    }
  ],
  "presets": [
    {
      "name": "Quiz result banner"
    }
  ]
}
{% endschema %}
